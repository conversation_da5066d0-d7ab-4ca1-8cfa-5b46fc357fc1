import { NextRequest } from 'next/server'
import OpenAI from 'openai'
import { scrapeWebPage } from '@/lib/scraper'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// 流式生成AI笔记
async function generateAINoteStream(content: string, title: string): Promise<ReadableStream> {
  // 检查是否有有效的OpenAI API密钥
  const apiKey = process.env.OPENAI_API_KEY
  if (!apiKey || apiKey === 'sk-test-key-for-development' || apiKey === 'your_openai_api_key_here') {
    // 返回模拟的流式AI笔记
    const mockNote = `# 📖 ${title} - 结构化AI笔记

---

## 📋 核心观点

- "${title}" 专为文档中的示例用途而设，无需事先协调或获得许可即可使用。

---

## 🔍 关键信息

- **用途**：用于文档、教材等中的举例说明。
- **许可**：无需事先沟通或获得授权，开放使用。  
- **适用范围**：可以在任何文学或文档中直接引用。

---

## 💡 实用价值

- 便于开发者、技术写作者在说明中使用标准化的域名示例。
- 避免使用真实存在的域名，减少误导和潜在法律风险。
- 提高文档的规范性和一致性。

---

## 🎯 总结

"${title}" 提供了一个安全、规范的示例域名，方便在各类文档中举例，无需任何许可，是文档标准化的重要资源。

💡 **提示**：这是演示模式生成的笔记。要获得真正的AI分析，请配置有效的OpenAI API密钥。`

    return new ReadableStream({
      start(controller) {
        const characters = mockNote.split('')
        let index = 0
        
        const sendNextChunk = () => {
          if (index < characters.length) {
            // 每次发送 1-3 个字符，模拟更真实的打字效果
            const chunkSize = Math.floor(Math.random() * 3) + 1
            const chunk = characters.slice(index, index + chunkSize).join('')
            
            controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ 
              content: chunk,
              isComplete: false 
            })}\n\n`))
            
            index += chunkSize
            setTimeout(sendNextChunk, Math.random() * 100 + 80) // 80-180ms 随机间隔，更明显
          } else {
            controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ 
              content: '',
              isComplete: true,
              fullContent: mockNote
            })}\n\n`))
            controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
            controller.close()
          }
        }
        
        setTimeout(sendNextChunk, 1200) // 延迟1.2秒开始，让用户看到状态变化
      }
    })
  }

  try {
    const systemPrompt = process.env.AI_SYSTEM_PROMPT || `你是一个专业的知识提炼助手。请将用户提供的内容转换为结构化的笔记，包含：

## 📋 核心观点
- 提取3-5个最重要的核心观点
- 每个观点用简洁的语言表达

## 🔍 关键信息
- 重要的数据、事实、引用
- 关键人物、时间、地点
- 专业术语解释

## 💡 实用价值
- 这些信息的实际应用场景
- 对读者的启发和建议
- 可行动的要点

## 🎯 总结
- 一句话总结全文精髓

请用Markdown格式输出，保持简洁明了，使用合适的emoji和格式化。`

    // 创建流式响应
    const stream = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: `请为以下内容生成结构化的AI笔记：

**标题：** ${title}

**内容：**
${content}

请按照系统提示的格式生成结构化笔记，确保内容有层次感和可读性。`
        }
      ],
      stream: true,
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2000'),
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7')
    })

    let fullContent = ''
    
    return new ReadableStream({
      async start(controller) {
        try {
          for await (const chunk of stream) {
            const content = chunk.choices[0]?.delta?.content || ''
            if (content) {
              fullContent += content
              controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ 
                content,
                isComplete: false 
              })}\n\n`))
            }
          }
          
          // 发送完成信号
          controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ 
            content: '',
            isComplete: true,
            fullContent 
          })}\n\n`))
          controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
          controller.close()
        } catch (error) {
          console.error('Streaming error:', error)
          controller.error(error)
        }
      }
    })

  } catch (error) {
    console.error('生成AI笔记失败:', error)
    
    // 返回错误流
    return new ReadableStream({
      start(controller) {
        const errorMessage = '抱歉，AI笔记生成失败，请稍后重试。'
        controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({ 
          content: errorMessage,
          isComplete: true,
          fullContent: errorMessage,
          error: true
        })}\n\n`))
        controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
        controller.close()
      }
    })
  }
}

export async function POST(request: NextRequest) {
  try {
    const { input, type } = await request.json()
    
    if (!input || !type) {
      return new Response('Missing required parameters', { status: 400 })
    }
    
    let title: string
    let content: string
    let textContent: string
    
    if (type === 'url') {
      // 使用更健壮的网页抓取逻辑
      try {
        const urlData = await scrapeWebPage(input)
        title = urlData.title
        content = urlData.content
        textContent = urlData.textContent
      } catch (error) {
        console.error('网页抓取失败:', error)
        return new Response(
          JSON.stringify({ 
            error: '无法获取网页内容。可能原因：1) 网站拒绝访问 2) 网络连接问题 3) 网页需要JavaScript渲染',
            success: false 
          }),
          { 
            status: 500,
            headers: { 'Content-Type': 'application/json' }
          }
        )
      }
    } else {
      // 处理文本
      title = '文本内容'
      content = `<div class="text-content">${input.replace(/\n/g, '<br>')}</div>`
      textContent = input
    }
    
    // 首先返回基本信息
    const basicInfo = {
      title,
      content,
      success: true
    }
    
    // 创建组合流：先发送基本信息，然后是AI笔记流
    const combinedStream = new ReadableStream({
      async start(controller) {
        // 发送基本信息
        controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify({
          type: 'basic_info',
          data: basicInfo
        })}\n\n`))
        
        // 生成AI笔记流
        const aiNoteStream = await generateAINoteStream(textContent, title)
        const reader = aiNoteStream.getReader()
        
        try {
          while (true) {
            const { done, value } = await reader.read()
            if (done) break
            
            // 转发AI笔记流数据，添加类型标识
            const decoder = new TextDecoder()
            const chunk = decoder.decode(value)
            const lines = chunk.split('\n')
            
            for (const line of lines) {
              if (line.startsWith('data: ')) {
                const data = line.slice(6)
                if (data === '[DONE]') {
                  controller.enqueue(new TextEncoder().encode('data: [DONE]\n\n'))
                  controller.close()
                  return
                }
                
                try {
                  const parsed = JSON.parse(data)
                  const wrappedData = {
                    type: 'ai_note',
                    data: parsed
                  }
                  controller.enqueue(new TextEncoder().encode(`data: ${JSON.stringify(wrappedData)}\n\n`))
                } catch (e) {
                  // 忽略解析错误
                }
              }
            }
          }
        } catch (error) {
          console.error('Stream processing error:', error)
          controller.error(error)
        } finally {
          reader.releaseLock()
        }
      }
    })

    return new Response(combinedStream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
      },
    })
    
  } catch (error) {
    console.error('处理请求失败:', error)
    return new Response(
      JSON.stringify({ 
        error: error instanceof Error ? error.message : '处理失败',
        success: false 
      }),
      { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    )
  }
} 